/**
 * 智合同聊天界面配置文件
 * 包含所有可配置的参数，如头像图片地址、WebSocket配置等
 */

const CONFIG = {
    // UI配置
    ui: {
        // AI助手头像图片地址
        aiAvatarUrl: 'src/images/logo.png',
        // 欢迎页面Logo地址
        welcomeLogoUrl: 'src/images/logo.png',
        // 应用标题
        appTitle: '智合同',
        // AI助手名称
        aiAssistantName: 'AI合同助手'
    },
    
    // WebSocket配置
    websocket: {
        // WebSocket服务器地址
        url: 'ws://localhost:8080/chat',
        // 是否使用模拟模式
        useSimulation: true,
        // 连接超时时间 (毫秒)
        connectionTimeout: 30000,
        // 重连配置
        reconnectAttempts: 5,
        reconnectDelay: 3000
    },
    
    // 打字效果配置
    typing: {
        // 模拟打字速度 (毫秒)
        speed: 50,
        // 思考过程打字速度倍率
        thinkingSpeedMultiplier: 0.8
    },
    
    // 合同模板配置
    contracts: {
        // 默认合同模板数据
        defaultTemplates: [
            {
                id: "12345",
                name: '劳动合同模板',
                content: '标准劳动合同，包含薪资、工作内容等条款'
            },
            {
                id: "12346",
                name: '租赁合同模板',
                content: '房屋租赁合同，包含租金、押金、维修等条款'
            },
            {
                id: "12347",
                name: '销售合同模板',
                content: '商品销售合同，包含价格、交付、质保等条款'
            },
            {
                id: "12348",
                name: '服务合同模板',
                content: '服务提供合同，包含服务内容、费用、期限等'
            }
        ]
    }
};

// 导出配置对象
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = CONFIG;
} else {
    // 浏览器环境
    window.CONFIG = CONFIG;
}
